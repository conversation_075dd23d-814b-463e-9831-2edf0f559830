import lietorch
import torch
from typing import List, Tuple
from logger import logger
from mast3r_slam.config import config
from mast3r_slam.frame import SharedKeyframes
from mast3r_slam.geometry import (
    constrain_points_to_ray,
)
from mast3r_slam.mast3r_utils import mast3r_match_symmetric
import mast3r_slam_backends
import numpy as np
import math
import os
import tempfile
from mast3r_slam.gauss_newton_python import (
    gauss_newton_rays_python,
    gauss_newton_calib_python
)
from thirdparty.mast3r.mast3r.cloud_opt.sparse_ga import sparse_scene_optimizer, SparseGA
from thirdparty.mast3r.dust3r.dust3r.utils.device import to_cpu, to_numpy, todevice
from thirdparty.mast3r.mast3r.utils.misc import mkdir_for, hash_md5
from thirdparty.mast3r.mast3r.cloud_opt.utils.losses import gamma_loss
from thirdparty.mast3r.mast3r.cloud_opt.utils.schedules import cosine_schedule


class FactorGraph:
    def __init__(self, model, frames: SharedKeyframes, K=None, device="cuda"):
        self.model = model
        self.frames = frames
        self.device = device
        # self.vertices = []
        # self.edges = []
        self.cfg = config["local_opt"]
        self.ii = torch.as_tensor([], dtype=torch.long, device=self.device)
        self.jj = torch.as_tensor([], dtype=torch.long, device=self.device)
        self.idx_ii2jj = torch.as_tensor([], dtype=torch.long, device=self.device)
        self.idx_jj2ii = torch.as_tensor([], dtype=torch.long, device=self.device)
        self.valid_match_j = torch.as_tensor([], dtype=torch.bool, device=self.device)
        self.valid_match_i = torch.as_tensor([], dtype=torch.bool, device=self.device)
        self.Q_ii2jj = torch.as_tensor([], dtype=torch.float32, device=self.device)
        self.Q_jj2ii = torch.as_tensor([], dtype=torch.float32, device=self.device)
        self.window_size = self.cfg["window_size"]

        # 添加frame_id到索引的映射
        self.fid_to_indice = {}  # frame_id -> index mapping

        self.K = K

    def _update_fid_to_indice_mapping(self):
        """更新frame_id到索引的映射"""
        self.fid_to_indice.clear()
        for idx in range(len(self.frames)):
            frame = self.frames[idx]
            if hasattr(frame, 'frame_id'):
                self.fid_to_indice[frame.frame_id] = idx
            elif hasattr(frame, 'dataset_idx'):
                # 对于SharedKeyframes，frame_id存储在dataset_idx中
                self.fid_to_indice[int(frame.dataset_idx)] = idx

    def get_index_from_frame_id(self, frame_id):
        """根据frame_id获取索引"""
        if frame_id not in self.fid_to_indice:
            self._update_fid_to_indice_mapping()
        return self.fid_to_indice.get(frame_id, None)

    def get_indices_from_frame_ids(self, frame_ids):
        """根据frame_id列表获取索引列表"""
        indices = []
        for frame_id in frame_ids:
            idx = self.get_index_from_frame_id(frame_id)
            if idx is not None:
                indices.append(idx)
        return indices

    @property
    def vertices(self):
        return list(range(len(self.frames)))
    
    @property
    def edges(self):
        ii = self.ii.cpu().numpy().tolist()
        jj = self.jj.cpu().numpy().tolist()
        return [(i, j) for i, j in zip(ii, jj)]

    def show(self):
        import plotly.graph_objects as go

        nodes = {}
        for i, frame in zip(self.vertices, self.frames):
            x, y, z = frame.T_WC.data.cpu().numpy()[0, :3].tolist()
            nodes[i] = (x, y, z)
        edges = self.edges

        # Create edge traces
        edge_traces = []
        for edge in edges:
            start_node = nodes[edge[0]]
            end_node = nodes[edge[1]]
            edge_trace = go.Scatter3d(
                x=[start_node[0], end_node[0], None],
                y=[start_node[1], end_node[1], None],
                z=[start_node[2], end_node[2], None],
                mode='lines',
                line=dict(width=1, color='blue'),
                hoverinfo='none'
            )
            edge_traces.append(edge_trace)

        # Create node trace
        node_trace = go.Scatter3d(
            x=[node[0] for node in nodes.values()],
            y=[node[1] for node in nodes.values()],
            z=[node[2] for node in nodes.values()],
            mode='markers+text',
            marker=dict(size=3, color='red', symbol='circle'),
            text=[str(i) for i in nodes.keys()],
            textposition="top center"
        )

        # Compile layout
        layout = go.Layout(
            title='3D Topology Graph',
            margin=dict(l=0, r=0, b=0, t=0),
            scene=dict(aspectmode='data')
        )

        # Compile figure
        fig = go.Figure(data=edge_traces + [node_trace], layout=layout)

        # Show figure
        fig.show()

    def add_factors(self, ii, jj, min_match_frac, is_reloc=False, use_frame_ids=False):
        """
        添加因子到图中

        Args:
            ii: 第一组帧的索引或frame_id列表
            jj: 第二组帧的索引或frame_id列表
            min_match_frac: 最小匹配分数
            is_reloc: 是否为重定位
            use_frame_ids: 如果为True，ii和jj被视为frame_id；否则视为索引
        """
        assert len(ii) == len(jj)

        if use_frame_ids:
            # 将frame_id转换为索引
            ii_indices = self.get_indices_from_frame_ids(ii)
            jj_indices = self.get_indices_from_frame_ids(jj)

            # 检查是否所有frame_id都找到了对应的索引
            if len(ii_indices) != len(ii) or len(jj_indices) != len(jj):
                missing_ii = [fid for fid in ii if self.get_index_from_frame_id(fid) is None]
                missing_jj = [fid for fid in jj if self.get_index_from_frame_id(fid) is None]
                logger.warning(f"Missing frame_ids in mapping: ii={missing_ii}, jj={missing_jj}")
                return False

            ii = ii_indices
            jj = jj_indices

        edges = [(i, j) for i, j in zip(ii, jj)]
        logger.info(f"Adding {len(edges)} factors {edges}")
        kf_ii = [self.frames[idx] for idx in ii]
        kf_jj = [self.frames[idx] for idx in jj]
        feat_i = torch.cat([kf_i.feat for kf_i in kf_ii])
        feat_j = torch.cat([kf_j.feat for kf_j in kf_jj])
        pos_i = torch.cat([kf_i.pos for kf_i in kf_ii])
        pos_j = torch.cat([kf_j.pos for kf_j in kf_jj])
        shape_i = [kf_i.img_true_shape for kf_i in kf_ii]
        shape_j = [kf_j.img_true_shape for kf_j in kf_jj]

        (
            idx_i2j,
            idx_j2i,
            valid_match_j,
            valid_match_i,
            Qii,
            Qjj,
            Qji,
            Qij,
        ) = mast3r_match_symmetric(
            self.model, feat_i, pos_i, feat_j, pos_j, shape_i, shape_j
        )

        batch_inds = torch.arange(idx_i2j.shape[0], device=idx_i2j.device)[
            :, None
        ].repeat(1, idx_i2j.shape[1])
        Qj = torch.sqrt(Qii[batch_inds, idx_i2j] * Qji)
        Qi = torch.sqrt(Qjj[batch_inds, idx_j2i] * Qij)

        valid_Qj = Qj > self.cfg["Q_conf"]
        valid_Qi = Qi > self.cfg["Q_conf"]
        valid_j = valid_match_j & valid_Qj
        valid_i = valid_match_i & valid_Qi
        nj = valid_j.shape[1] * valid_j.shape[2]
        ni = valid_i.shape[1] * valid_i.shape[2]
        match_frac_j = valid_j.sum(dim=(1, 2)) / nj
        match_frac_i = valid_i.sum(dim=(1, 2)) / ni

        ii_tensor = torch.as_tensor(ii, device=self.device)
        jj_tensor = torch.as_tensor(jj, device=self.device)

        # NOTE: Saying we need both edge directions to be above thrhreshold to accept either
        invalid_edges = torch.minimum(match_frac_j, match_frac_i) < min_match_frac
        consecutive_edges = ii_tensor == (jj_tensor - 1)
        invalid_edges = (~consecutive_edges) & invalid_edges
        logger.debug(f"{match_frac_j=}")
        logger.debug(f"{match_frac_i=}")
        logger.debug(f"{min_match_frac=}")
        logger.debug(f"{consecutive_edges=}")
        logger.debug(f"{invalid_edges=}")

        if invalid_edges.any() and is_reloc:
            return False

        valid_edges = ~invalid_edges
        ii_tensor = ii_tensor[valid_edges]
        jj_tensor = jj_tensor[valid_edges]
        idx_i2j = idx_i2j[valid_edges]
        idx_j2i = idx_j2i[valid_edges]
        valid_match_j = valid_match_j[valid_edges]
        valid_match_i = valid_match_i[valid_edges]
        Qj = Qj[valid_edges]
        Qi = Qi[valid_edges]

        self.ii = torch.cat([self.ii, ii_tensor])
        self.jj = torch.cat([self.jj, jj_tensor])
        self.idx_ii2jj = torch.cat([self.idx_ii2jj, idx_i2j])
        self.idx_jj2ii = torch.cat([self.idx_jj2ii, idx_j2i])
        self.valid_match_j = torch.cat([self.valid_match_j, valid_match_j])
        self.valid_match_i = torch.cat([self.valid_match_i, valid_match_i])
        self.Q_ii2jj = torch.cat([self.Q_ii2jj, Qj])
        self.Q_jj2ii = torch.cat([self.Q_jj2ii, Qi])

        # for i in range(len(valid_edges)):
        #     if valid_edges[i]:
        #         if (ii[i], jj[i]) not in self.edges:
        #             self.edges.append((ii[i], jj[i]))
        #         if ii[i] not in self.vertices:
        #             self.vertices.append(ii[i])
        #         if jj[i] not in self.vertices:
        #             self.vertices.append(jj[i])
        # self.vertices = list(set(self.vertices))

        logger.info(f"{edges} edges: {torch.minimum(match_frac_i, match_frac_j)} over {min_match_frac}")
        logger.info(f"added edges: {[e for e, v in zip(edges, valid_edges) if v]}")

        added_new_edges = valid_edges.sum() > 0
        return added_new_edges

    def add_factors_by_frame_ids(self, frame_ids_i, frame_ids_j, min_match_frac, is_reloc=False):
        """
        使用frame_id添加因子的便捷方法

        Args:
            frame_ids_i: 第一组frame_id列表
            frame_ids_j: 第二组frame_id列表
            min_match_frac: 最小匹配分数
            is_reloc: 是否为重定位
        """
        return self.add_factors(frame_ids_i, frame_ids_j, min_match_frac, is_reloc, use_frame_ids=True)

    def remove_frame_from_mapping(self, frame_id):
        """
        从映射中移除指定的frame_id

        Args:
            frame_id: 要移除的frame_id
        """
        if frame_id in self.fid_to_indice:
            del self.fid_to_indice[frame_id]

    def remove_edges_with_frame_id(self, frame_id):
        """
        移除包含指定frame_id的所有边

        Args:
            frame_id: 要移除的frame_id
        """
        # 首先获取该frame_id对应的索引
        frame_idx = self.get_index_from_frame_id(frame_id)
        if frame_idx is None:
            return

        # 找到包含该索引的边
        mask_ii = self.ii != frame_idx
        mask_jj = self.jj != frame_idx
        valid_mask = mask_ii & mask_jj

        # 保留不包含该索引的边
        self.ii = self.ii[valid_mask]
        self.jj = self.jj[valid_mask]
        self.idx_ii2jj = self.idx_ii2jj[valid_mask]
        self.idx_jj2ii = self.idx_jj2ii[valid_mask]
        self.valid_match_j = self.valid_match_j[valid_mask]
        self.valid_match_i = self.valid_match_i[valid_mask]
        self.Q_ii2jj = self.Q_ii2jj[valid_mask]
        self.Q_jj2ii = self.Q_jj2ii[valid_mask]

    def update_indices_after_frame_removal(self, removed_idx):
        """
        在删除关键帧后更新所有索引

        Args:
            removed_idx: 被删除的帧的索引
        """
        # 更新边中的索引：所有大于removed_idx的索引都需要减1
        self.ii = torch.where(self.ii > removed_idx, self.ii - 1, self.ii)
        self.jj = torch.where(self.jj > removed_idx, self.jj - 1, self.jj)

        # 重新构建frame_id到索引的映射
        self._update_fid_to_indice_mapping()

    def get_unique_kf_idx(self):
        return torch.unique(torch.cat([self.ii, self.jj]), sorted=True)

    def prep_two_way_edges(self):
        ii = torch.cat((self.ii, self.jj), dim=0)
        jj = torch.cat((self.jj, self.ii), dim=0)
        idx_ii2jj = torch.cat((self.idx_ii2jj, self.idx_jj2ii), dim=0)
        valid_match = torch.cat((self.valid_match_j, self.valid_match_i), dim=0)
        Q_ii2jj = torch.cat((self.Q_ii2jj, self.Q_jj2ii), dim=0)
        return ii, jj, idx_ii2jj, valid_match, Q_ii2jj

    def get_poses_points(self, unique_kf_idx):
        kfs = [self.frames[idx.item()] for idx in unique_kf_idx]
        Xs = torch.stack([kf.X_canon for kf in kfs])
        T_WCs = lietorch.Sim3(torch.stack([kf.T_WC.data for kf in kfs]))

        Cs = torch.stack([kf.get_average_conf() for kf in kfs])

        return Xs, T_WCs, Cs

    def solve_GN_rays(self):
        pin = self.cfg["pin"]
        unique_kf_idx = self.get_unique_kf_idx()
        n_unique_kf = unique_kf_idx.numel()
        if n_unique_kf <= pin:
            return

        logger.debug(f"Running GN on rays: {unique_kf_idx}")
        Xs, T_WCs, Cs = self.get_poses_points(unique_kf_idx)

        ii, jj, idx_ii2jj, valid_match, Q_ii2jj = self.prep_two_way_edges()

        C_thresh = self.cfg["C_conf"]
        Q_thresh = self.cfg["Q_conf"]
        max_iter = self.cfg["max_iters"]
        sigma_ray = self.cfg["sigma_ray"]
        sigma_dist = self.cfg["sigma_dist"]
        delta_thresh = self.cfg["delta_norm"]

        pose_data = T_WCs.data[:, 0, :]
        mast3r_slam_backends.gauss_newton_rays(
            pose_data,
            Xs,
            Cs,
            ii,
            jj,
            idx_ii2jj,
            valid_match,
            Q_ii2jj,
            sigma_ray,
            sigma_dist,
            C_thresh,
            Q_thresh,
            max_iter,
            delta_thresh,
        )

        # Update the keyframe T_WC
        self.frames.update_T_WCs(T_WCs[pin:], unique_kf_idx[pin:])

    def solve_GN_calib(self, K=None):
        if K is None:
            K = self.K
        pin = self.cfg["pin"]
        unique_kf_idx = self.get_unique_kf_idx()
        n_unique_kf = unique_kf_idx.numel()
        if n_unique_kf <= pin:
            return

        Xs, T_WCs, Cs = self.get_poses_points(unique_kf_idx)

        # Constrain points to ray
        img_size = self.frames[0].img.shape[-2:]
        Xs = constrain_points_to_ray(img_size, Xs, K)

        ii, jj, idx_ii2jj, valid_match, Q_ii2jj = self.prep_two_way_edges()

        C_thresh = self.cfg["C_conf"]
        Q_thresh = self.cfg["Q_conf"]
        pixel_border = self.cfg["pixel_border"]
        z_eps = self.cfg["depth_eps"]
        max_iter = self.cfg["max_iters"]
        sigma_pixel = self.cfg["sigma_pixel"]
        sigma_depth = self.cfg["sigma_depth"]
        delta_thresh = self.cfg["delta_norm"]

        pose_data = T_WCs.data[:, 0, :]

        img_size = self.frames[0].img.shape[-2:]
        height, width = img_size

        mast3r_slam_backends.gauss_newton_calib(
            pose_data,
            Xs,
            Cs,
            K,
            ii,
            jj,
            idx_ii2jj,
            valid_match,
            Q_ii2jj,
            height,
            width,
            pixel_border,
            z_eps,
            sigma_pixel,
            sigma_depth,
            C_thresh,
            Q_thresh,
            max_iter,
            delta_thresh,
        )

        # Update the keyframe T_WC
        self.frames.update_T_WCs(T_WCs[pin:], unique_kf_idx[pin:])

    def solve_GN_rays_python(self):
        """Python implementation of Gauss-Newton ray alignment optimization."""
        pin = self.cfg["pin"]
        unique_kf_idx = self.get_unique_kf_idx()
        n_unique_kf = unique_kf_idx.numel()
        if n_unique_kf <= pin:
            return

        logger.debug(f"Running GN on rays (Python): {unique_kf_idx}")
        Xs, T_WCs, Cs = self.get_poses_points(unique_kf_idx)

        ii, jj, idx_ii2jj, valid_match, Q_ii2jj = self.prep_two_way_edges()

        C_thresh = self.cfg["C_conf"]
        Q_thresh = self.cfg["Q_conf"]
        max_iter = self.cfg["max_iters"]
        sigma_ray = self.cfg["sigma_ray"]
        sigma_dist = self.cfg["sigma_dist"]
        delta_thresh = self.cfg["delta_norm"]

        pose_data = T_WCs.data[:, 0, :]
        gauss_newton_rays_python(
            pose_data,
            Xs,
            Cs,
            ii,
            jj,
            idx_ii2jj,
            valid_match,
            Q_ii2jj,
            sigma_ray,
            sigma_dist,
            C_thresh,
            Q_thresh,
            max_iter,
            delta_thresh,
        )

        # Update the keyframe T_WC
        self.frames.update_T_WCs(T_WCs[pin:], unique_kf_idx[pin:])

    def solve_GN_calib_python(self):
        """Python implementation of Gauss-Newton calibrated camera optimization."""
        K = self.K
        pin = self.cfg["pin"]
        unique_kf_idx = self.get_unique_kf_idx()
        n_unique_kf = unique_kf_idx.numel()
        if n_unique_kf <= pin:
            return

        Xs, T_WCs, Cs = self.get_poses_points(unique_kf_idx)

        # Constrain points to ray
        img_size = self.frames[0].img.shape[-2:]
        Xs = constrain_points_to_ray(img_size, Xs, K)

        ii, jj, idx_ii2jj, valid_match, Q_ii2jj = self.prep_two_way_edges()

        C_thresh = self.cfg["C_conf"]
        Q_thresh = self.cfg["Q_conf"]
        pixel_border = self.cfg["pixel_border"]
        z_eps = self.cfg["depth_eps"]
        max_iter = self.cfg["max_iters"]
        sigma_pixel = self.cfg["sigma_pixel"]
        sigma_depth = self.cfg["sigma_depth"]
        delta_thresh = self.cfg["delta_norm"]

        pose_data = T_WCs.data[:, 0, :]

        img_size = self.frames[0].img.shape[-2:]
        height, width = img_size

        gauss_newton_calib_python(
            pose_data,
            Xs,
            Cs,
            K,
            ii,
            jj,
            idx_ii2jj,
            valid_match,
            Q_ii2jj,
            height,
            width,
            pixel_border,
            z_eps,
            sigma_pixel,
            sigma_depth,
            C_thresh,
            Q_thresh,
            max_iter,
            delta_thresh,
        )

        # Update the keyframe T_WC
        self.frames.update_T_WCs(T_WCs[pin:], unique_kf_idx[pin:])

    def optimize_with_sparse_ga(self, cache_path=None, subsample=8, 
                               lr1=0.2, niter1=500, lr2=0.02, niter2=500,
                               shared_intrinsics=False, matching_conf_thr=5.0):
        """
        Optimize the factor graph using MASt3R's sparse scene optimizer.
        Uses the existing edges and features in the factor graph.
        
        Args:
            cache_path: Path to store temporary files
            subsample: Subsampling factor for point clouds
            lr1, niter1: Learning rate and iterations for coarse alignment
            lr2, niter2: Learning rate and iterations for refinement
            shared_intrinsics: Whether to use shared intrinsics for all cameras
            matching_conf_thr: Confidence threshold for matching
        
        Returns:
            Optimization results
        """
        logger.info("Optimizing with MASt3R sparse scene optimizer using existing edges and features")
        
        # Create temporary cache directory if not provided
        if cache_path is None:
            cache_path = tempfile.mkdtemp(prefix="sparse_ga_cache_")
        os.makedirs(cache_path, exist_ok=True)
        
        # Prepare image paths and data structures
        imgs = []
        canonical_paths = []
        
        # Create virtual image paths for all frames
        for i in range(len(self.frames)):
            imgs.append(f"frame_{i}")
        
        # Prepare data structures for sparse_scene_optimizer
        imsizes = []
        pps = []
        base_focals = []
        core_depth = []
        anchors = {}
        
        # Extract data from frames
        for i, frame in enumerate(self.frames):
            # Get image size
            H, W = frame.img_shape.cpu().numpy()
            imsizes.append(torch.tensor([W, H], device=self.device))
            
            # Get principal point (default to center if not available)
            if hasattr(frame, 'K') and frame.K is not None:
                pp = frame.K[:2, 2].to(self.device)
            else:
                pp = torch.tensor([W/2, H/2], device=self.device)
            pps.append(pp)
            
            # Get focal length (default to 1.0 if not available)
            if hasattr(frame, 'K') and frame.K is not None:
                focal = frame.K[0, 0].to(self.device).view(1)
            else:
                focal = torch.tensor([1.0], device=self.device)
            base_focals.append(focal)
            
            # Get depth map from X_canon if available
            if hasattr(frame, 'X_canon') and frame.X_canon is not None:
                # Subsample the depth map
                X_canon = frame.X_canon[::subsample, ::subsample]
                depth = X_canon[..., 2].reshape(-1)
                core_depth.append(depth)
                
                # Create canonical path for this frame
                canon_path = os.path.join(cache_path, f'canon_views/{hash_md5(imgs[i])}_subsample={subsample}.pth')
                canonical_paths.append(canon_path)
                
                # Save canonical data if not already saved
                if not os.path.exists(canon_path):
                    # Create canonical data
                    canon = frame.X_canon
                    canon2 = frame.X_canon  # Use same for both
                    conf = frame.C if hasattr(frame, 'C') and frame.C is not None else torch.ones_like(canon[..., 0])
                    
                    # Save to file
                    os.makedirs(os.path.dirname(canon_path), exist_ok=True)
                    torch.save(to_cpu(((canon, canon2, conf), focal)), canon_path)
            else:
                # If no depth map, create a dummy one
                logger.warning(f"Frame {i} has no X_canon, using dummy depth")
                H_sub, W_sub = H // subsample, W // subsample
                depth = torch.ones(H_sub * W_sub, device=self.device)
                core_depth.append(depth)
                
                # Create dummy canonical path
                canon_path = os.path.join(cache_path, f'canon_views/{hash_md5(imgs[i])}_subsample={subsample}.pth')
                canonical_paths.append(canon_path)
                
                # Save dummy canonical data
                if not os.path.exists(canon_path):
                    # Create dummy canonical data
                    canon = torch.zeros((H, W, 3), device=self.device)
                    canon[..., 2] = 1.0  # Set Z to 1.0
                    canon2 = canon.clone()
                    conf = torch.ones((H, W), device=self.device)
                    
                    # Save to file
                    os.makedirs(os.path.dirname(canon_path), exist_ok=True)
                    torch.save(to_cpu(((canon, canon2, conf), focal)), canon_path)
            
            # Create anchors for this frame
            pixels = torch.from_numpy(np.mgrid[:W_sub, :H_sub].T.reshape(-1, 2)).float().to(self.device)
            idxs = torch.arange(H_sub * W_sub, device=self.device)
            offsets = torch.ones(H_sub * W_sub, device=self.device)
            anchors[i] = (pixels, idxs, offsets)
        
        # Prepare correspondences using existing matches in factor graph
        corres = []
        corres2d = {i: [] for i in range(len(self.frames))}
        
        # Use existing edges and matches from the factor graph
        for edge_idx in range(len(self.ii)):
            i, j = self.ii[edge_idx].item(), self.jj[edge_idx].item()
            
            # Get matches for this edge
            idx_i2j = self.idx_ii2jj[edge_idx]
            valid_match = self.valid_match_j[edge_idx]
            
            # Extract valid matches
            valid_indices = torch.where(valid_match.view(-1))[0]
            if len(valid_indices) > 0:
                # Get pixel coordinates for matches
                H_i, W_i = self.frames[i].img_shape.cpu().numpy()
                H_j, W_j = self.frames[j].img_shape.cpu().numpy()
                
                # Create grid of pixel coordinates
                y_i, x_i = torch.meshgrid(
                    torch.arange(H_i, device=self.device),
                    torch.arange(W_i, device=self.device),
                    indexing='ij'
                )
                pixels_i = torch.stack([x_i.reshape(-1), y_i.reshape(-1)], dim=-1)
                
                y_j, x_j = torch.meshgrid(
                    torch.arange(H_j, device=self.device),
                    torch.arange(W_j, device=self.device),
                    indexing='ij'
                )
                pixels_j = torch.stack([x_j.reshape(-1), y_j.reshape(-1)], dim=-1)
                
                # Get matched pixels
                matched_i = pixels_i[valid_indices]
                matched_j = pixels_j[idx_i2j.view(-1)[valid_indices]]
                
                # Get match confidences
                confs = self.Q_ii2jj[edge_idx].view(-1)[valid_indices]
                
                # Add to correspondences
                corres.append((matched_i, matched_j, confs))
                
                # Add to 2D correspondences
                corres2d[i].append((matched_i, confs, j, slice(0, len(matched_i))))
                corres2d[j].append((matched_j, confs, i, slice(0, len(matched_j))))
        
        # Prepare initial poses from frames
        init = {}
        for i, frame in enumerate(self.frames):
            # Convert from lietorch Sim3 to transformation matrix
            T_WC = frame.T_WC.matrix().cpu().numpy()[0]
            # sparse_ga expects camera-to-world transforms
            T_CW = np.linalg.inv(T_WC)
            init[imgs[i]] = {'pose': torch.tensor(T_CW, device=self.device)}
            
            # Add intrinsics if available
            if hasattr(frame, 'K') and frame.K is not None:
                init[imgs[i]]['intrinsics'] = frame.K.to(self.device)
        
        # Create a custom MST structure that matches our factor graph
        edges = [(i.item(), j.item()) for i, j in zip(self.ii, self.jj) if i.item() < j.item()]
        root = 0  # Use the first frame as root
        mst = (root, edges)
        
        # Run sparse scene optimizer
        imgs, res_coarse, res_fine = sparse_scene_optimizer(
            imgs, subsample, imsizes, pps, base_focals, core_depth, anchors, 
            corres, corres2d, None, canonical_paths, mst,
            shared_intrinsics=shared_intrinsics, cache_path=cache_path, 
            device=self.device, dtype=torch.float32,
            lr1=lr1, niter1=niter1, loss1=gamma_loss(1.1),
            lr2=lr2, niter2=niter2, loss2=gamma_loss(0.4),
            lossd=gamma_loss(1.1),
            opt_pp=True, opt_depth=True,
            schedule=cosine_schedule, depth_mode='add',
            matching_conf_thr=matching_conf_thr,
            init=init
        )
        
        # Create a result object
        result = {'intrinsics': res_fine['intrinsics'] if res_fine else res_coarse['intrinsics'],
                 'cam2w': res_fine['cam2w'] if res_fine else res_coarse['cam2w'],
                 'depthmaps': res_fine['depthmaps'] if res_fine else res_coarse['depthmaps']}
        
        # Update frame poses from optimization result
        self._update_poses_from_sparse_ga(result)
        
        return result
    
    def _update_poses_from_sparse_ga(self, result):
        """
        Update frame poses from optimization results
        
        Args:
            result: Dictionary with optimization results
        """
        # Extract optimized poses
        K = result['intrinsics']
        cam2w = result['cam2w']
        
        # Update each frame's pose
        for i, frame in enumerate(self.frames):
            if i < cam2w.shape[0]:
                # Get camera-to-world transform
                c2w = cam2w[i].cpu().numpy()
                # Convert to world-to-camera
                w2c = np.linalg.inv(c2w)
                
                # Extract rotation and translation
                R = w2c[:3, :3]
                t = w2c[:3, 3]
                
                # Convert to Sim3 (scale is 1.0 for SE3)
                scale = 1.0
                
                # Update frame pose using lietorch
                sim3_data = torch.zeros(8, device=self.device)
                sim3_data[:3] = torch.tensor(t, device=self.device)
                
                # Convert rotation matrix to quaternion
                from scipy.spatial.transform import Rotation
                quat = Rotation.from_matrix(R).as_quat()  # x,y,z,w format
                sim3_data[3] = torch.tensor(quat[3], device=self.device)  # w
                sim3_data[4:7] = torch.tensor(quat[:3], device=self.device)  # x,y,z
                sim3_data[7] = torch.tensor(scale, device=self.device)
                
                # Update frame pose
                frame.T_WC = lietorch.Sim3(sim3_data.unsqueeze(0))
        
        logger.info("Updated frame poses from sparse scene optimization")
        
        # If K was optimized and we want to update camera intrinsics
        if K is not None:
            for i, frame in enumerate(self.frames):
                if hasattr(frame, 'K') and i < len(K):
                    frame.K = K[i].to(self.device)
            logger.info("Updated camera intrinsics from sparse scene optimization")

    def optimize_with_custom_sparse_ga(self, cache_path=None, subsample=8, 
                                  lr1=0.2, niter1=500, lr2=0.02, niter2=500,
                                  shared_intrinsics=False, matching_conf_thr=5.0):
        """
        Optimize the factor graph using a custom implementation of MASt3R's sparse scene optimizer.
        Adapted to work with FactorGraph structure and avoids in-place operations.
        
        Args:
            cache_path: Path to store temporary files
            subsample: Subsampling factor for point clouds
            lr1, niter1: Learning rate and iterations for coarse alignment
            lr2, niter2: Learning rate and iterations for refinement
            shared_intrinsics: Whether to use shared intrinsics for all cameras
            matching_conf_thr: Confidence threshold for matching
        
        Returns:
            Optimization results
        """
        import torch.nn as nn
        import torch.nn.functional as F
        from thirdparty.mast3r.mast3r.cloud_opt.utils.losses import gamma_loss
        from thirdparty.mast3r.mast3r.cloud_opt.utils.schedules import cosine_schedule
        from thirdparty.mast3r.dust3r.dust3r.utils.device import to_cpu, to_numpy, todevice
        from thirdparty.mast3r.mast3r.utils.misc import mkdir_for, hash_md5
        from thirdparty.mast3r.mast3r.cloud_opt.sparse_ga import make_pts3d, inv, geotrf, reproj2d
        from tqdm import tqdm
        
        logger.info("Optimizing with custom sparse scene optimizer adapted for FactorGraph")
        
        # Create temporary cache directory if not provided
        if cache_path is None:
            cache_path = tempfile.mkdtemp(prefix="sparse_ga_cache_")
        os.makedirs(cache_path, exist_ok=True)
        
        # Get device
        device = self.device
        
        # Prepare data structures
        n_frames = len(self.frames)
        imgs = [f.img_path for f in self.frames]
        
        # Create initial parameters for optimization
        # Avoid in-place operations by creating new tensors
        
        # Create quaternions and translations
        quats = []
        trans = []
        log_sizes = []
        
        for i in range(n_frames):
            # Extract pose from frame
            T_WC = self.frames[i].T_WC
            
            # Convert to quaternion and translation
            quat = torch.tensor([0.0, 0.0, 0.0, 1.0], dtype=torch.float32, device=device)
            quat = nn.Parameter(quat)
            
            # Extract translation
            t = torch.zeros(3, dtype=torch.float32, device=device)
            t = nn.Parameter(t)
            
            # Add log size parameter
            log_size = torch.zeros(1, dtype=torch.float32, device=device)
            log_size = nn.Parameter(log_size)
            
            quats.append(quat)
            trans.append(t)
            log_sizes.append(log_size)
        
        # Create intrinsics parameters
        pps = []
        log_focals = []
        
        if shared_intrinsics:
            # Use shared intrinsics for all cameras
            pp = torch.tensor([0.0, 0.0], dtype=torch.float32, device=device)
            pp = nn.Parameter(pp)
            
            log_focal = torch.tensor([5.0], dtype=torch.float32, device=device)  # log(150) ≈ 5
            log_focal = nn.Parameter(log_focal)
            
            for i in range(n_frames):
                pps.append(pp)
                log_focals.append(log_focal)
        else:
            # Use separate intrinsics for each camera
            for i in range(n_frames):
                pp = torch.tensor([0.0, 0.0], dtype=torch.float32, device=device)
                pp = nn.Parameter(pp)
                
                log_focal = torch.tensor([5.0], dtype=torch.float32, device=device)
                log_focal = nn.Parameter(log_focal)
                
                pps.append(pp)
                log_focals.append(log_focal)
        
        # Create depth parameters
        core_depth = []
        for i in range(n_frames):
            # Create a simple depth map
            H, W = self.frames[i].img_shape
            H_sub = H // subsample
            W_sub = W // subsample
            
            # Initialize with constant depth
            depth = torch.ones(H_sub * W_sub, dtype=torch.float32, device=device)
            depth = nn.Parameter(depth)
            
            core_depth.append(depth)
        
        # Create anchors (pixel coordinates)
        anchors = []
        for i in range(n_frames):
            H, W = self.frames[i].img_shape
            H_sub = H // subsample
            W_sub = W // subsample
            
            # Create grid of pixel coordinates
            y, x = torch.meshgrid(
                torch.arange(0, H, subsample, device=device),
                torch.arange(0, W, subsample, device=device),
                indexing='ij'
            )
            
            # Flatten and stack
            pixels = torch.stack([x.flatten(), y.flatten()], dim=-1)
            anchors.append(pixels)
        
        # Prepare correspondences
        corres_filtered = []
        for i, j in zip(self.ii, self.jj):
            i_idx = i.item()
            j_idx = j.item()
            
            # Get matches between frames i and j
            matches = self.get_matches(i_idx, j_idx)
            if matches is None or len(matches) == 0:
                continue
            
            # Extract pixel coordinates and confidences
            pix_i = matches[:, :2]  # x, y coordinates in frame i
            pix_j = matches[:, 2:4]  # x, y coordinates in frame j
            confs = matches[:, 4]    # match confidence
            
            # Filter by confidence
            mask = confs > matching_conf_thr
            if mask.sum() == 0:
                continue
                
            pix_i = pix_i[mask]
            pix_j = pix_j[mask]
            confs = confs[mask]
            
            corres_filtered.append((i_idx, j_idx, pix_i, pix_j, confs))
        
        # Define loss functions
        loss_3d_func = gamma_loss(1.1)
        loss_2d_func = gamma_loss(0.4)
        
        # Define function to make K, camera matrices, and depth maps
        def make_K_cam_depth(log_focals, pps, trans, quats, log_sizes, core_depth):
            # Create intrinsics matrix K
            K = torch.zeros(n_frames, 3, 3, device=device)
            
            for i in range(n_frames):
                focal = torch.exp(log_focals[i])
                pp = pps[i]
                
                # Create K matrix
                K[i, 0, 0] = focal
                K[i, 1, 1] = focal
                K[i, 0, 2] = pp[0]
                K[i, 1, 2] = pp[1]
                K[i, 2, 2] = 1.0
            
            # Create camera matrices
            w2cam = torch.zeros(n_frames, 3, 4, device=device)
            cam2w = torch.zeros(n_frames, 4, 4, device=device)
            
            for i in range(n_frames):
                # Normalize quaternion
                q = quats[i] / quats[i].norm()
                
                # Convert quaternion to rotation matrix
                R = torch.zeros(3, 3, device=device)
                
                # Fill rotation matrix (simplified version)
                R[0, 0] = 1 - 2 * (q[1]**2 + q[2]**2)
                R[0, 1] = 2 * (q[0] * q[1] - q[2] * q[3])
                R[0, 2] = 2 * (q[0] * q[2] + q[1] * q[3])
                
                R[1, 0] = 2 * (q[0] * q[1] + q[2] * q[3])
                R[1, 1] = 1 - 2 * (q[0]**2 + q[2]**2)
                R[1, 2] = 2 * (q[1] * q[2] - q[0] * q[3])
                
                R[2, 0] = 2 * (q[0] * q[2] - q[1] * q[3])
                R[2, 1] = 2 * (q[1] * q[2] + q[0] * q[3])
                R[2, 2] = 1 - 2 * (q[0]**2 + q[1]**2)
                
                # Set translation
                t = trans[i]
                
                # Create w2cam matrix
                w2cam[i, :3, :3] = R
                w2cam[i, :3, 3] = t
                
                # Create cam2w matrix
                cam2w[i, :3, :3] = R.transpose(0, 1)
                cam2w[i, :3, 3] = -R.transpose(0, 1) @ t
                cam2w[i, 3, 3] = 1.0
            
            # Create depth maps
            depthmaps = []
            for i in range(n_frames):
                H, W = self.frames[i].img_shape
                H_sub = H // subsample
                W_sub = W // subsample
                
                # Reshape depth
                depth = core_depth[i].reshape(H_sub, W_sub)
                
                # Apply scaling
                scale = torch.exp(log_sizes[i])
                depth = depth * scale
                
                depthmaps.append(depth)
            
            return K, (w2cam, cam2w), depthmaps
        
        # Define function to create 3D points from depth maps
        def make_pts3d(anchors, K, cam2w, depthmaps, base_focals=None):
            pts3d = []
            
            for i in range(n_frames):
                # Get pixel coordinates
                pixels = anchors[i]
                
                # Get depth map
                depth = depthmaps[i].flatten()
                
                # Get camera intrinsics
                K_i = K[i]
                
                # Get camera extrinsics
                cam2w_i = cam2w[i]
                
                # Compute 3D points in camera coordinates
                # First, convert pixels to normalized coordinates
                fx = K_i[0, 0]
                fy = K_i[1, 1]
                cx = K_i[0, 2]
                cy = K_i[1, 2]
                
                x = (pixels[:, 0] - cx) / fx
                y = (pixels[:, 1] - cy) / fy
                
                # Create 3D points in camera coordinates
                pts3d_cam = torch.stack([x * depth, y * depth, depth], dim=-1)
                
                # Transform to world coordinates
                # Extract rotation and translation
                R = cam2w_i[:3, :3]
                t = cam2w_i[:3, 3]
                
                # Apply transformation
                pts3d_world = pts3d_cam @ R.T + t
                
                pts3d.append(pts3d_world)
            
            return pts3d
        
        # Define 3D loss function
        def compute_loss_3d(K, w2cam, pts3d):
            loss = 0.0
            npix = 0.0
            
            for edge_idx, (i, j) in enumerate(zip(self.ii, self.jj)):
                i_idx = i.item()
                j_idx = j.item()
                
                # 获取匹配
                idx_i2j = self.idx_ii2jj[edge_idx]
                valid_match = self.valid_match_j[edge_idx]
                confs = self.Q_ii2jj[edge_idx]
                
                # 只保留有效的匹配
                valid_idx = valid_match.squeeze(-1)
                if valid_idx.sum() == 0:
                    continue
                
                idx_i2j = idx_i2j[valid_idx]
                confs = confs[valid_idx]
                
                # 获取3D点
                sampled_pts3d_i = pts3d[i_idx][idx_i2j]
                
                # 获取对应的j中的点索引
                j_indices = torch.arange(pts3d[j_idx].shape[0], device=self.device)[valid_idx]
                
                # 确保j中的点索引有效
                if j_indices.shape[0] != idx_i2j.shape[0]:
                    logger.warning(f"Mismatch in point counts: i={idx_i2j.shape[0]}, j={j_indices.shape[0]}")
                    # 使用较小的数量
                    min_count = min(idx_i2j.shape[0], j_indices.shape[0])
                    idx_i2j = idx_i2j[:min_count]
                    j_indices = j_indices[:min_count]
                    confs = confs[:min_count]
                    sampled_pts3d_i = sampled_pts3d_i[:min_count]
                
                sampled_pts3d_j = pts3d[j_idx][j_indices]
                
                # 计算世界坐标系中的点
                R_i_inv = w2cam[i_idx, :3, :3].transpose(0, 1)
                t_i = w2cam[i_idx, :3, 3]
                pts3d_i_world = sampled_pts3d_i @ R_i_inv - t_i @ R_i_inv
                
                R_j_inv = w2cam[j_idx, :3, :3].transpose(0, 1)
                t_j = w2cam[j_idx, :3, 3]
                pts3d_j_world = sampled_pts3d_j @ R_j_inv - t_j @ R_j_inv
                
                # 确保点数量匹配
                if pts3d_i_world.shape[0] != pts3d_j_world.shape[0]:
                    logger.warning(f"Mismatch in world point counts: i={pts3d_i_world.shape[0]}, j={pts3d_j_world.shape[0]}")
                    min_count = min(pts3d_i_world.shape[0], pts3d_j_world.shape[0])
                    pts3d_i_world = pts3d_i_world[:min_count]
                    pts3d_j_world = pts3d_j_world[:min_count]
                    confs = confs[:min_count]
                
                # 计算3D点距离
                point_dists = torch.norm(pts3d_i_world - pts3d_j_world, dim=1)
                
                # 确保置信度形状匹配
                if confs.shape[0] != point_dists.shape[0]:
                    logger.warning(f"Mismatch in confidence and distance counts: conf={confs.shape[0]}, dist={point_dists.shape[0]}")
                    min_count = min(confs.shape[0], point_dists.shape[0])
                    confs = confs[:min_count]
                    point_dists = point_dists[:min_count]
                
                # 加权距离
                weighted_dists = point_dists * confs.squeeze(-1)
                loss = loss + weighted_dists.sum()
                npix = npix + confs.sum()
            
            return loss / (npix + 1e-8)
        
        # Define 2D loss function
        def compute_loss_2d(K, w2cam, pts3d):
            loss = 0.0
            npix = 0.0
            
            for edge_idx, (i, j) in enumerate(zip(self.ii, self.jj)):
                i_idx = i.item()
                j_idx = j.item()
                
                # 获取匹配
                idx_i2j = self.idx_ii2jj[edge_idx]
                valid_match = self.valid_match_j[edge_idx]
                confs = self.Q_ii2jj[edge_idx]
                
                # 只保留有效的匹配
                valid_idx = valid_match.squeeze(-1)
                if valid_idx.sum() == 0:
                    continue
                
                idx_i2j = idx_i2j[valid_idx]
                confs = confs[valid_idx]
                
                # 获取像素坐标
                h, w = self.frames[i_idx].img_shape
                y_i = torch.div(idx_i2j, w, rounding_mode='floor')
                x_i = idx_i2j - y_i * w
                pix_i = torch.stack([x_i, y_i], dim=1).float()
                
                # 获取对应的j中的点索引
                j_indices = torch.arange(pts3d[j_idx].shape[0], device=self.device)[valid_idx]
                
                # 确保j中的点索引有效
                if j_indices.shape[0] != idx_i2j.shape[0]:
                    logger.warning(f"Mismatch in point counts: i={idx_i2j.shape[0]}, j={j_indices.shape[0]}")
                    # 使用较小的数量
                    min_count = min(idx_i2j.shape[0], j_indices.shape[0])
                    idx_i2j = idx_i2j[:min_count]
                    j_indices = j_indices[:min_count]
                    confs = confs[:min_count]
                    pix_i = pix_i[:min_count]
                
                sampled_pts3d_j = pts3d[j_idx][j_indices]
                
                # 变换到相机i坐标系
                R_i = w2cam[i_idx, :3, :3]
                t_i = w2cam[i_idx, :3, 3]
                pts3d_i_cam = sampled_pts3d_j @ R_i.T + t_i
                
                # 投影到2D
                z_i = pts3d_i_cam[:, 2:3]
                z_i = torch.clamp(z_i, min=1e-3)  # 避免除以零
                
                proj_i = pts3d_i_cam[:, :2] / z_i
                proj_i = proj_i @ K[i_idx, :2, :2].T + K[i_idx, :2, 2]
                
                # 确保像素坐标和投影坐标的数量匹配
                if pix_i.shape[0] != proj_i.shape[0]:
                    logger.warning(f"Mismatch in pixel and projection counts: pix={pix_i.shape[0]}, proj={proj_i.shape[0]}")
                    min_count = min(pix_i.shape[0], proj_i.shape[0])
                    pix_i = pix_i[:min_count]
                    proj_i = proj_i[:min_count]
                    confs = confs[:min_count]
                
                # 计算2D重投影误差
                reproj_dists = torch.norm(pix_i - proj_i, dim=1)
                
                # 确保置信度形状匹配
                if confs.shape[0] != reproj_dists.shape[0]:
                    logger.warning(f"Mismatch in confidence and distance counts: conf={confs.shape[0]}, dist={reproj_dists.shape[0]}")
                    min_count = min(confs.shape[0], reproj_dists.shape[0])
                    confs = confs[:min_count]
                    reproj_dists = reproj_dists[:min_count]
                
                # 加权距离
                weighted_dists = reproj_dists * confs.squeeze(-1)
                loss = loss + weighted_dists.sum()
                npix = npix + confs.sum()
                
                # 同样在另一个方向上计算（j到i）
                h_j, w_j = self.frames[j_idx].img_shape
                y_j = torch.div(j_indices, w_j, rounding_mode='floor')
                x_j = j_indices - y_j * w_j
                pix_j = torch.stack([x_j, y_j], dim=1).float()
                
                sampled_pts3d_i = pts3d[i_idx][idx_i2j]
                
                # 变换到相机j坐标系
                R_j = w2cam[j_idx, :3, :3]
                t_j = w2cam[j_idx, :3, 3]
                pts3d_j_cam = sampled_pts3d_i @ R_j.T + t_j
                
                # 投影到2D
                z_j = pts3d_j_cam[:, 2:3]
                z_j = torch.clamp(z_j, min=1e-3)  # 避免除以零
                
                proj_j = pts3d_j_cam[:, :2] / z_j
                proj_j = proj_j @ K[j_idx, :2, :2].T + K[j_idx, :2, 2]
                
                # 确保像素坐标和投影坐标的数量匹配
                if pix_j.shape[0] != proj_j.shape[0]:
                    logger.warning(f"Mismatch in pixel and projection counts: pix={pix_j.shape[0]}, proj={proj_j.shape[0]}")
                    min_count = min(pix_j.shape[0], proj_j.shape[0])
                    pix_j = pix_j[:min_count]
                    proj_j = proj_j[:min_count]
                    confs = confs[:min_count] if confs.shape[0] > min_count else confs
                
                # 计算2D重投影误差
                reproj_dists = torch.norm(pix_j - proj_j, dim=1)
                
                # 确保置信度形状匹配
                if confs.shape[0] != reproj_dists.shape[0]:
                    logger.warning(f"Mismatch in confidence and distance counts: conf={confs.shape[0]}, dist={reproj_dists.shape[0]}")
                    min_count = min(confs.shape[0], reproj_dists.shape[0])
                    confs = confs[:min_count]
                    reproj_dists = reproj_dists[:min_count]
                
                # 加权距离
                weighted_dists = reproj_dists * confs.squeeze(-1)
                loss = loss + weighted_dists.sum()
                npix = npix + confs.sum()
            
            return loss / (npix + 1e-8)
        
        # Define optimization function
        def optimize_loop(loss_type, lr_base=0.1, lr_end=1e-4, niter=100):
            # Collect parameters
            params = []
            params.extend(quats)
            params.extend(trans)
            params.extend(log_sizes)
            
            if shared_intrinsics:
                params.append(pps[0])
                params.append(log_focals[0])
            else:
                params.extend(pps)
                params.extend(log_focals)
            
            params.extend(core_depth)
            
            # Create optimizer
            optimizer = torch.optim.Adam(params, lr=lr_base)
            
            # Define function to adjust learning rate
            def adjust_learning_rate_by_lr(optimizer, lr):
                for param_group in optimizer.param_groups:
                    param_group['lr'] = lr
            
            # Run optimization
            with tqdm(total=niter) as bar:
                for iter in range(niter or 1):
                    # Create a new computation graph for each iteration
                    K, (w2cam, cam2w), depthmaps = make_K_cam_depth(log_focals, pps, trans, quats, log_sizes, core_depth)
                    pts3d = make_pts3d(anchors, K, cam2w, depthmaps)
                    
                    if niter == 0:
                        break
                    
                    alpha = (iter / niter)
                    lr = cosine_schedule(alpha, lr_base, lr_end)
                    adjust_learning_rate_by_lr(optimizer, lr)
                    
                    optimizer.zero_grad()
                    
                    # Compute loss based on loss type
                    if loss_type == '3d':
                        loss = compute_loss_3d(K, w2cam, pts3d)
                    else:  # '2d'
                        loss = compute_loss_2d(K, w2cam, pts3d)
                    
                    # Check if loss is valid
                    if not torch.isfinite(loss):
                        logger.warning(f"Loss is not finite: {loss.item()}, skipping backward")
                        continue
                    
                    # Create a fresh computation graph for each iteration
                    loss.backward()
                    
                    # Check for NaN gradients
                    has_nan = False
                    for param in params:
                        if param.grad is not None and torch.isnan(param.grad).any():
                            has_nan = True
                            break
                        
                    if has_nan:
                        logger.warning("NaN gradients detected, skipping update")
                        optimizer.zero_grad()
                        continue
                        
                    optimizer.step()
                    
                    # Make sure quaternions remain normalized
                    for i in range(n_frames):
                        q_norm = quats[i].data.norm()
                        if q_norm > 0:
                            quats[i].data = quats[i].data / q_norm
                    
                    bar.update(1)
                    bar.set_description(f"Loss: {loss.item():.6f}")
            
            # Return final results
            K, (w2cam, cam2w), depthmaps = make_K_cam_depth(log_focals, pps, trans, quats, log_sizes, core_depth)
            pts3d = make_pts3d(anchors, K, cam2w, depthmaps)
            
            return {
                'intrinsics': K.detach(),
                'cam2w': cam2w.detach(),
                'depthmaps': [d.detach() for d in depthmaps],
                'pts3d': [p.detach() for p in pts3d]
            }
        
        # Run coarse optimization (3D alignment)
        logger.info("Running coarse optimization (3D alignment)")
        res_coarse = optimize_loop('3d', lr_base=lr1, niter=niter1)
        
        # Run fine optimization (2D reprojection)
        res_fine = None
        if niter2 > 0:
            logger.info("Running fine optimization (2D reprojection)")
            # Enable optimization of principal points and depth
            for i in range(n_frames):
                pps[i].requires_grad_(True)
                log_focals[i].requires_grad_(True)
                core_depth[i].requires_grad_(True)
            
            res_fine = optimize_loop('2d', lr_base=lr2, niter=niter2)
        
        # Create result object
        result = res_fine if res_fine is not None else res_coarse
        
        # Update frame poses from optimization result
        self._update_poses_from_custom_sparse_ga(result)
        
        return result

    def _update_poses_from_custom_sparse_ga(self, result):
        """
        Update frame poses from custom sparse GA optimization results
        
        Args:
            result: Dictionary with optimization results
        """
        # Extract optimized poses and intrinsics
        K = result['intrinsics']
        cam2w = result['cam2w']
        
        # Update frame poses
        for i, frame in enumerate(self.frames):
            if i < len(cam2w):
                # Extract rotation and translation
                R = cam2w[i, :3, :3]
                t = cam2w[i, :3, 3]
                
                # Convert to lietorch Sim3
                import lietorch
                
                # Create Sim3 transformation (scale is set to 1.0 for now)
                sim3_data = torch.zeros(8, device=self.device)
                sim3_data[:3] = t  # translation
                
                # Convert rotation matrix to quaternion
                from scipy.spatial.transform import Rotation
                R_np = R.detach().cpu().numpy()
                quat = Rotation.from_matrix(R_np).as_quat()  # x,y,z,w format
                sim3_data[3] = torch.tensor(quat[3], device=self.device)  # w
                sim3_data[4:7] = torch.tensor(quat[:3], device=self.device)  # x,y,z
                sim3_data[7] = torch.tensor(1.0, device=self.device)  # scale
                
                # Update frame pose
                frame.T_WC = lietorch.Sim3(sim3_data.unsqueeze(0))
        
        logger.info("Updated frame poses from custom sparse scene optimization")
        
        # Update camera intrinsics if available
        if K is not None:
            for i, frame in enumerate(self.frames):
                if hasattr(frame, 'K') and i < len(K):
                    frame.K = K[i].to(self.device)
            logger.info("Updated camera intrinsics from custom sparse scene optimization")

    def get_matches(self, i, j):
        """
        获取两个帧之间的匹配
        
        Args:
            i: 第一个帧的索引
            j: 第二个帧的索引
        
        Returns:
            matches: 形状为 [N, 5] 的张量，包含匹配的像素坐标和置信度
                     每行为 [x_i, y_i, x_j, y_j, conf]
        """
        # 查找 i 和 j 在 self.ii 和 self.jj 中的位置
        mask_i_to_j = (self.ii == i) & (self.jj == j)
        mask_j_to_i = (self.ii == j) & (self.jj == i)
        
        if mask_i_to_j.any():
            # 找到了从 i 到 j 的边
            edge_idx = mask_i_to_j.nonzero().item()
            
            # 获取匹配
            idx_i2j = self.idx_ii2jj[edge_idx]
            valid_match = self.valid_match_j[edge_idx]
            Q = self.Q_ii2jj[edge_idx]
            
            # 只保留有效的匹配
            valid_idx = valid_match.squeeze(-1)
            if valid_idx.sum() == 0:
                return None
            
            idx_i2j = idx_i2j[valid_idx]
            Q = Q[valid_idx]
            
            # 将索引转换为像素坐标
            h, w = self.frames[i].img_shape
            
            # 计算 i 中的像素坐标
            y_i = torch.div(idx_i2j, w, rounding_mode='floor')
            x_i = idx_i2j - y_i * w
            
            # 计算 j 中的像素坐标
            h_j, w_j = self.frames[j].img_shape
            lin_idx_j = torch.arange(h_j * w_j, device=self.device)
            y_j = torch.div(lin_idx_j, w_j, rounding_mode='floor')
            x_j = lin_idx_j - y_j * w_j
            
            # 获取匹配的 j 中的像素坐标
            x_j = x_j[idx_i2j]
            y_j = y_j[idx_i2j]
            
            # 创建匹配张量
            matches = torch.stack([x_i.float(), y_i.float(), x_j.float(), y_j.float(), Q.squeeze(-1)], dim=1)
            return matches
        
        elif mask_j_to_i.any():
            # 找到了从 j 到 i 的边，需要交换顺序
            edge_idx = mask_j_to_i.nonzero().item()
            
            # 获取匹配
            idx_j2i = self.idx_ii2jj[edge_idx]
            valid_match = self.valid_match_j[edge_idx]
            Q = self.Q_ii2jj[edge_idx]
            
            # 只保留有效的匹配
            valid_idx = valid_match.squeeze(-1)
            if valid_idx.sum() == 0:
                return None
            
            idx_j2i = idx_j2i[valid_idx]
            Q = Q[valid_idx]
            
            # 将索引转换为像素坐标
            h_j, w_j = self.frames[j].img_shape
            
            # 计算 j 中的像素坐标
            y_j = torch.div(idx_j2i, w_j, rounding_mode='floor')
            x_j = idx_j2i - y_j * w_j
            
            # 计算 i 中的像素坐标
            h, w = self.frames[i].img_shape
            lin_idx_i = torch.arange(h * w, device=self.device)
            y_i = torch.div(lin_idx_i, w, rounding_mode='floor')
            x_i = lin_idx_i - y_i * w
            
            # 获取匹配的 i 中的像素坐标
            x_i = x_i[idx_j2i]
            y_i = y_i[idx_j2i]
            
            # 创建匹配张量
            matches = torch.stack([x_i.float(), y_i.float(), x_j.float(), y_j.float(), Q.squeeze(-1)], dim=1)
            return matches
        
        else:
            # 没有找到边
            return None
