#!/usr/bin/env python3
"""
测试FactorGraph的frame_id索引功能
"""

import torch
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from mast3r_slam.global_opt import FactorGraph
from mast3r_slam.frame import Frame, SharedKeyframes
import lietorch

def create_test_frame(frame_id, device="cuda"):
    """创建测试用的Frame"""
    h, w = 224, 224
    frame = Frame(
        frame_id=frame_id,
        img=torch.randn(3, h, w, device=device),
        img_shape=torch.tensor([[h, w]], device=device),
        img_true_shape=torch.tensor([[h, w]], device=device),
        uimg=torch.randn(h, w, 3, device="cpu"),
        T_WC=lietorch.Sim3.Identity(1, device=device)
    )
    # 添加一些基本的特征
    frame.feat = torch.randn(1, 196, 512, device=device)  # 假设的特征维度
    frame.pos = torch.randint(0, h, (1, 196, 2), device=device)
    frame.X_canon = torch.randn(h*w, 3, device=device)
    frame.C = torch.randn(h*w, 1, device=device)
    frame.N = 100
    frame.N_updates = 0
    return frame

def test_frame_id_mapping():
    """测试frame_id到索引的映射功能"""
    print("Testing frame_id to index mapping...")
    
    device = "cuda" if torch.cuda.is_available() else "cpu"
    
    # 创建SharedKeyframes
    frames = SharedKeyframes(buffer=10, device=device)
    
    # 创建测试帧
    frame1 = create_test_frame(100, device)  # frame_id = 100
    frame2 = create_test_frame(200, device)  # frame_id = 200
    frame3 = create_test_frame(300, device)  # frame_id = 300
    
    # 添加到SharedKeyframes
    frames.append(frame1)  # index 0
    frames.append(frame2)  # index 1
    frames.append(frame3)  # index 2
    
    # 创建FactorGraph（使用mock model）
    class MockModel:
        def _encode_image(self, img, img_shape):
            h, w = img_shape[0]
            feat = torch.randn(1, 196, 512, device=img.device)
            pos = torch.randint(0, h, (1, 196, 2), device=img.device)
            return feat, pos, None
    
    model = MockModel()
    factor_graph = FactorGraph(model, frames, device=device)
    
    # 测试映射功能
    print("Testing get_index_from_frame_id...")
    assert factor_graph.get_index_from_frame_id(100) == 0
    assert factor_graph.get_index_from_frame_id(200) == 1
    assert factor_graph.get_index_from_frame_id(300) == 2
    assert factor_graph.get_index_from_frame_id(999) is None
    print("✓ get_index_from_frame_id works correctly")
    
    # 测试批量映射
    print("Testing get_indices_from_frame_ids...")
    indices = factor_graph.get_indices_from_frame_ids([100, 200, 300])
    assert indices == [0, 1, 2]
    
    indices = factor_graph.get_indices_from_frame_ids([200, 100])
    assert indices == [1, 0]
    
    indices = factor_graph.get_indices_from_frame_ids([100, 999, 200])
    assert indices == [0, 1]  # 999不存在，被跳过
    print("✓ get_indices_from_frame_ids works correctly")
    
    # 测试使用frame_id添加因子
    print("Testing add_factors with frame_ids...")
    success = factor_graph.add_factors([100, 200], [200, 300], 0.1, use_frame_ids=True)
    print(f"add_factors result: {success}")
    
    # 检查边是否正确添加
    print(f"Edges: {factor_graph.edges}")
    print(f"ii: {factor_graph.ii}")
    print(f"jj: {factor_graph.jj}")
    
    # 测试便捷方法
    print("Testing add_factors_by_frame_ids...")
    success2 = factor_graph.add_factors_by_frame_ids([300], [100], 0.1)
    print(f"add_factors_by_frame_ids result: {success2}")
    
    print(f"Final edges: {factor_graph.edges}")
    
    print("✓ All frame_id mapping tests passed!")

def test_frame_removal():
    """测试帧删除时的索引更新"""
    print("\nTesting frame removal and index updates...")
    
    device = "cuda" if torch.cuda.is_available() else "cpu"
    
    # 创建SharedKeyframes
    frames = SharedKeyframes(buffer=10, device=device)
    
    # 创建测试帧
    frame1 = create_test_frame(100, device)
    frame2 = create_test_frame(200, device)
    frame3 = create_test_frame(300, device)
    
    frames.append(frame1)  # index 0
    frames.append(frame2)  # index 1
    frames.append(frame3)  # index 2
    
    class MockModel:
        def _encode_image(self, img, img_shape):
            h, w = img_shape[0]
            feat = torch.randn(1, 196, 512, device=img.device)
            pos = torch.randint(0, h, (1, 196, 2), device=img.device)
            return feat, pos, None
    
    model = MockModel()
    factor_graph = FactorGraph(model, frames, device=device)
    
    # 添加一些边
    factor_graph.add_factors([0, 1], [1, 2], 0.1)  # 使用索引
    print(f"Initial edges: {factor_graph.edges}")
    
    # 模拟删除中间的帧（index 1, frame_id 200）
    print("Simulating removal of frame at index 1...")
    
    # 删除frame_id映射
    factor_graph.remove_frame_from_mapping(200)
    
    # 删除包含该frame_id的边
    factor_graph.remove_edges_with_frame_id(200)
    
    # 更新索引（模拟frames.pop操作后的状态）
    factor_graph.update_indices_after_frame_removal(1)
    
    print(f"Edges after removal: {factor_graph.edges}")
    print(f"ii after removal: {factor_graph.ii}")
    print(f"jj after removal: {factor_graph.jj}")
    
    print("✓ Frame removal test completed!")

if __name__ == "__main__":
    test_frame_id_mapping()
    test_frame_removal()
    print("\n🎉 All tests completed successfully!")
